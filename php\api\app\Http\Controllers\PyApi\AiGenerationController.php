<?php

namespace App\Http\Controllers\PyApi;

use App\Enums\ApiCodeEnum;
use App\Http\Controllers\Controller;
use App\Services\AuthService;
use App\Services\PyApi\AiGenerationService;
use App\Services\PyApi\WebSocketService;
use App\Jobs\ProcessTextGeneration;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use App\Helpers\LogCheckHelper;

/**
 * AI内容生成与任务管理-向第三方AI平台（虚拟AI API服务）发起请求
 */
class AiGenerationController extends Controller
{
    protected $aiGenerationService;

    public function __construct(AiGenerationService $aiGenerationService)
    {
        $this->aiGenerationService = $aiGenerationService;
    }

    /**
     * @ApiTitle(获取生成任务状态)
     * @ApiSummary(查询AI生成任务的状态和结果)
     * @ApiMethod(GET)
     * @ApiRoute(/py-api/ai/tasks/{id})
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="id", type="int", required=true, description="任务ID")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="任务数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "id": 123,
     *     "task_type": "text_generation",
     *     "status": "completed",
     *     "platform": "deepseek",
     *     "model_name": "deepseek-chat",
     *     "input_data": {
     *       "prompt": "生成一个故事"
     *     },
     *     "output_data": {
     *       "text": "这是生成的故事..."
     *     },
     *     "cost": "0.0150",
     *     "tokens_used": 150,
     *     "processing_time_ms": 1200,
     *     "created_at": "2024-01-01 12:00:00",
     *     "completed_at": "2024-01-01 12:00:01"
     *   }
     * })
     */
    public function getTaskStatus(Request $request, $id)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(
                    $authResult['response']['code'],
                    $authResult['response']['message'],
                    $authResult['response']['data']
                );
            }

            $user = $authResult['user'];
            $result = $this->aiGenerationService->getTaskStatus((int)$id, $user->id);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('获取AI任务状态失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取AI任务状态失败', []);
        }
    }

    /**
     * @ApiTitle(获取用户生成任务列表)
     * @ApiSummary(获取用户的AI生成任务历史)
     * @ApiMethod(GET)
     * @ApiRoute(/py-api/ai/tasks)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="task_type", type="string", required=false, description="任务类型筛选")
     * @ApiParams(name="status", type="string", required=false, description="状态筛选")
     * @ApiParams(name="platform", type="string", required=false, description="平台筛选")
     * @ApiParams(name="page", type="int", required=false, description="页码，默认1")
     * @ApiParams(name="per_page", type="int", required=false, description="每页数量，默认20")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "tasks": [
     *       {
     *         "id": 123,
     *         "task_type": "text_generation",
     *         "status": "completed",
     *         "platform": "deepseek",
     *         "cost": "0.0150",
     *         "created_at": "2024-01-01 12:00:00"
     *       }
     *     ],
     *     "pagination": {
     *       "current_page": 1,
     *       "total": 50,
     *       "per_page": 20
     *     }
     *   }
     * })
     */
    public function getUserTasks(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(
                    $authResult['response']['code'],
                    $authResult['response']['message'],
                    $authResult['response']['data']
                );
            }

            $user = $authResult['user'];
            $page = $request->get('page', 1);
            $perPage = min($request->get('per_page', 20), 100);

            $filters = [
                'task_type' => $request->get('task_type'),
                'status' => $request->get('status'),
                'platform' => $request->get('platform')
            ];

            $result = $this->aiGenerationService->getUserTasks(
                $user->id,
                $filters,
                $page,
                $perPage
            );

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('获取用户AI任务列表失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取用户AI任务列表失败', []);
        }
    }

    /**
     * @ApiTitle(重试失败的任务)
     * @ApiSummary(重新执行失败的AI生成任务)
     * @ApiMethod(POST)
     * @ApiRoute(/py-api/ai/tasks/{id}/retry)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="id", type="int", required=true, description="任务ID")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "任务重试成功",
     *   "data": {
     *     "task_id": 123,
     *     "status": "pending",
     *     "retry_count": 1
     *   }
     * })
     */
    public function retryTask(Request $request, $id)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(
                    $authResult['response']['code'],
                    $authResult['response']['message'],
                    $authResult['response']['data']
                );
            }

            $user = $authResult['user'];
            $result = $this->aiGenerationService->retryTask($id, $user->id);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('重试AI任务失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '重试AI任务失败', []);
        }
    }

    /**
     * 生成文本（WebSocket版本）
     * @ApiTitle(生成文本-WebSocket版本)
     * @ApiMethod(POST)
     * @ApiRoute(/py-api/ai/text/generate-with-websocket)
     * @ApiParams(name="prompt", type="string", required=true, description="生成提示词")
     * @ApiParams(name="model_id", type="int", required=false, description="模型ID")
     * @ApiParams(name="project_id", type="int", required=false, description="项目ID")
     * @ApiParams(name="max_tokens", type="int", required=false, description="最大token数")
     * @ApiParams(name="temperature", type="float", required=false, description="温度参数")
     * @ApiParams(name="context", type="string", required=false, description="业务上下文")
     * @ApiParams(name="websocket_session_id", type="string", required=false, description="WebSocket会话ID")
     * @ApiReturnParams(name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams(name="message", type="string", required=true, description="消息")
     * @ApiReturnParams(name="data", type="object", required=true, description="任务信息")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "文本生成任务创建成功",
     *   "data": {
     *     "task_id": "text_gen_123456",
     *     "status": "processing",
     *     "context": "prompt_edit",
     *     "estimated_cost": 5.0
     *   }
     * })
     */
    public function generateTextWithWebSocket(Request $request)
    {
        try {
            $rules = [
                'prompt' => 'required|string|min:1|max:4000',
                'model_id' => 'sometimes|integer|exists:ai_model_configs,id',
                'project_id' => 'sometimes|integer|exists:projects,id',
                'max_tokens' => 'sometimes|integer|min:1|max:4000',
                'temperature' => 'sometimes|numeric|min:0|max:2',
                'top_p' => 'sometimes|numeric|min:0|max:1',
                'context' => 'sometimes|string|max:100',
                'websocket_session_id' => 'sometimes|string'
            ];

            $messages = [
                'prompt.required' => '提示词不能为空',
                'prompt.min' => '提示词不能为空',
                'prompt.max' => '提示词不能超过4000字符',
                'model_id.exists' => '指定的模型不存在',
                'project_id.exists' => '指定的项目不存在',
                'max_tokens.min' => '最大token数必须大于0',
                'max_tokens.max' => '最大token数不能超过4000',
                'temperature.min' => '温度参数不能小于0',
                'temperature.max' => '温度参数不能大于2',
                'top_p.min' => 'top_p参数不能小于0',
                'top_p.max' => 'top_p参数不能大于1'
            ];

            $this->validateData($request->all(), $rules, $messages, []);

            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];
            $webSocketSessionId = $request->get('websocket_session_id');

            // 如果有WebSocket会话ID，验证会话
            if ($webSocketSessionId) {
                $webSocketService = app(WebSocketService::class);
                $sessionValid = $webSocketService->validateSession($webSocketSessionId, $user->id);
                if (!$sessionValid) {
                    return $this->errorResponse(ApiCodeEnum::VALIDATION_ERROR, 'WebSocket会话无效', []);
                }
            }

            $generationParams = [
                'max_tokens' => $request->get('max_tokens', 1000),
                'temperature' => $request->get('temperature', 0.7),
                'top_p' => $request->get('top_p', 0.9)
            ];

            $context = $request->get('context', 'prompt_edit');

            // 🎯 调用服务层进行积分预检查
            $pointsCheckResult = $this->aiGenerationService->checkPointsForWebSocketTextGeneration(
                $user->id,
                $request->get('prompt'),
                $request->get('model_id'),
                $generationParams
            );

            // 积分检查失败，直接返回错误，不创建异步任务
            if ($pointsCheckResult['code'] !== ApiCodeEnum::SUCCESS) {
                return $this->errorResponse(
                    $pointsCheckResult['code'],
                    $pointsCheckResult['message'],
                    $pointsCheckResult['data'] ?? []
                );
            }

            // 积分充足，创建异步任务
            $taskId = 'text_gen_' . time() . '_' . Str::random(8);
            $prompt = $request->get('prompt');
            $modelId = $request->get('model_id');
            $estimatedCost = $pointsCheckResult['data']['estimated_cost'];

            // 创建异步任务
            dispatch(new ProcessTextGeneration(
                $taskId,
                $user->id,
                $prompt,
                $modelId,
                $request->get('project_id'),
                $generationParams,
                $context
            ));

            // 返回任务信息
            $responseData = [
                'task_id' => $taskId,
                'status' => 'processing',
                'context' => $context,
                'estimated_cost' => $estimatedCost,
                'prompt' => substr($prompt, 0, 100),
                'generation_params' => $generationParams,
                'websocket_session_id' => $webSocketSessionId,
                'timestamp' => \Carbon\Carbon::now()->toISOString(),
                'request_id' => 'req_' . Str::random(16)
            ];

            return $this->successResponse($responseData, '文本生成任务创建成功');

        } catch (\Exception $e) {
            Log::error('创建文本生成任务失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '创建文本生成任务失败: ' . $e->getMessage(), []);
        }
    }
}
